const express = require("express");
const bodyParser = require("body-parser");
const path = require("path");
const pool = require("./db");

const app = express();

const port = 8080; // port chạy app

// Cấu hình static files
app.use(express.static(path.join(__dirname, "public")));

// Cấu hình EJS
app.set("views", path.join(__dirname, "views"));
app.set("view engine", "ejs");

app.use(bodyParser.urlencoded({ extended: true }));

// Route chính
app.get("/", (req, res) => {
  res.render("index", { title: "Xin chào từ Express + EJS" });
});

// Route lấy dữ liệu từ database
app.get("/users", async (req, res) => {
  try {
    const result = await pool.query("SELECT * FROM truong_users"); // bảng users
    res.json(result.rows);
  } catch (err) {
    console.error(err);
    res.status(500).send("Lỗi server");
  }
});

// Route cac trang con
app.get("/test01", (req, res) => {
  res.render("test01");
});

app.get("/test022", (req, res) => {
  res.render("test022");
});

app.get("/test03", (req, res) => {
  res.render("test03");
});

app.get("/test04", (req, res) => {
  res.render("test04");
});

app.get("/test05", (req, res) => {
  res.render("test05");
});

app.get("/test06", (req, res) => {
  res.render("test06");
});

// Chạy server
app.listen(port, "0.0.0.0", () => {
  console.log(`🚀 Server đang chạy tại http://localhost:${port}`);
});
