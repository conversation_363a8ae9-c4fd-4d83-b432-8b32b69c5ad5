const express = require("express");
const path = require("path");

const app = express();

const port = 8080; // port chạy app

// Cấu hình EJS
app.set("views", path.join(__dirname, "views"));
app.set("view engine", "ejs");

// Route chính
app.get("/", (req, res) => {
  res.render("index", { title: "Xin chào từ Express + EJS" });
});

app.get("/test01", (req, res) => {
  res.render("test01");
});

app.get("/test02", (req, res) => {
  res.render("test02");
});

// Chạy server
app.listen(port, "0.0.0.0", () => {
  console.log(`App đang chạy tại http://localhost:${port}`);
});
