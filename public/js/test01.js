function test01() {
  document.getElementById("noidung01").innerHTML =
    "Da thay doi noi dung roi nhe!";
  document.getElementById("noidung01").style.color = "blue";
  document.getElementById("noidung01").style.fontSize = "48px";
}

let hamcong = function (a, b) {
  return a + b;
};

let tong = hamcong(15, 10);

function test02() {
  document.getElementById("noidung02").innerHTML = tong;
  document.getElementById("noidung02").style.color = "red";
  document.getElementById("noidung02").style.fontSize = "36px";
}

document.getElementById("tinh").onclick = function () {
  let x = document.getElementById("so1").value;
  let y = x * 10;
  document.getElementById("noidung04").textContent = y;

  document.getElementById("btntru1").onclick = function () {
    y--;
    document.getElementById("noidung04").innerHTML = y;
  };

  document.getElementById("btnreset").onclick = function () {
    document.getElementById("noidung04").innerHTML = 0;
  };

  document.getElementById("btncong1").onclick = function () {
    y++;
    document.getElementById("noidung04").innerHTML = y;
  };
};

// let number = document.getElementById("noidung04").value;
